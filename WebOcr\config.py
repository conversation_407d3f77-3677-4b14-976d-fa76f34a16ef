"""
配置管理模組
使用 Pydantic Settings 來管理應用程式配置
支援從環境變數和 .env 檔案載入配置
"""

from typing import Set, Optional
from pydantic import Field
from pydantic_settings import BaseSettings


class OCRSettings(BaseSettings):
    """OCR 服務配置"""
    
    # 服務基本配置
    app_name: str = Field(default="Advanced OCR Service", description="應用程式名稱")
    debug: bool = Field(default=False, description="除錯模式")
    host: str = Field(default="0.0.0.0", description="服務監聽位址")
    port: int = Field(default=8086, description="服務監聽埠號")
    
    # 檔案上傳限制
    max_file_size: int = Field(default=10 * 1024 * 1024, description="最大檔案大小（位元組），預設 10MB")
    allowed_extensions: Set[str] = Field(
        default={".jpg", ".jpeg", ".png", ".bmp", ".tiff", ".webp"}, 
        description="允許的檔案副檔名"
    )
    allowed_mime_types: Set[str] = Field(
        default={"image/jpeg", "image/png", "image/bmp", "image/tiff", "image/webp"},
        description="允許的 MIME 類型"
    )
    
    # PaddleOCR 配置
    ocr_language: str = Field(default="ch", description="OCR 識別語言")
    ocr_use_angle_cls: bool = Field(default=True, description="是否使用方向分類器")
    ocr_use_gpu: bool = Field(default=False, description="是否使用 GPU 加速")
    ocr_gpu_mem: int = Field(default=500, description="GPU 記憶體限制（MB）")
    
    # CPU 優化設定
    ocr_enable_mkldnn: bool = Field(default=True, description="是否啟用 MKL-DNN CPU 加速")
    ocr_cpu_threads: int = Field(default=8, description="CPU 執行緒數量")
    
    # 高性能設定
    ocr_enable_hpi: bool = Field(default=False, description="是否啟用高性能推理")
    ocr_use_tensorrt: bool = Field(default=False, description="是否使用 TensorRT 加速")
    ocr_precision: str = Field(default="fp32", description="計算精度 (fp32, fp16)")
    
    # 併發控制
    max_concurrent_requests: int = Field(default=5, description="最大同時處理請求數量")
    request_timeout: int = Field(default=30, description="請求逾時時間（秒）")
    
    # 預處理參數
    preprocessing_median_blur_kernel: int = Field(default=3, description="中值濾波核心大小")
    preprocessing_adaptive_threshold_block_size: int = Field(default=11, description="自適應二值化區塊大小")
    preprocessing_adaptive_threshold_c: int = Field(default=2, description="自適應二值化常數")
    
    # 日誌配置
    log_level: str = Field(default="INFO", description="日誌等級")
    log_format: str = Field(default="json", description="日誌格式 (json, text)")
    log_file: Optional[str] = Field(default=None, description="日誌檔案路徑")
    
    # 監控配置
    enable_metrics: bool = Field(default=True, description="是否啟用性能指標收集")
    metrics_port: int = Field(default=8002, description="指標服務埠號")
    
    model_config = {
        "env_file": ".env",  # 從 .env 檔案載入環境變數
        "env_file_encoding": "utf-8",
        "env_prefix": "OCR_",  # 環境變數前綴，例如 OCR_DEBUG=true
        "case_sensitive": False,  # 不區分大小寫
    }


# 建立全域配置實例
settings = OCRSettings()


def get_settings() -> OCRSettings:
    """獲取應用程式配置實例"""
    return settings


def reload_settings() -> OCRSettings:
    """重新載入配置（用於動態更新配置）"""
    global settings
    settings = OCRSettings()
    return settings


# 配置驗證函式
def validate_settings(settings: OCRSettings) -> bool:
    """驗證配置是否有效"""
    try:
        # 驗證埠號範圍
        if not (1 <= settings.port <= 65535):
            raise ValueError(f"無效的埠號: {settings.port}")
        
        if not (1 <= settings.metrics_port <= 65535):
            raise ValueError(f"無效的指標埠號: {settings.metrics_port}")
        
        # 驗證檔案大小限制
        if settings.max_file_size <= 0:
            raise ValueError("檔案大小限制必須大於 0")
        
        # 驗證 CPU 執行緒數
        if settings.ocr_cpu_threads <= 0:
            raise ValueError("CPU 執行緒數必須大於 0")
        
        # 驗證併發請求數量
        if settings.max_concurrent_requests <= 0:
            raise ValueError("最大併發請求數量必須大於 0")
        
        # 驗證精度設定
        if settings.ocr_precision not in ["fp32", "fp16", "int8"]:
            raise ValueError(f"不支援的計算精度: {settings.ocr_precision}")
        
        # 驗證日誌等級
        valid_log_levels = ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]
        if settings.log_level.upper() not in valid_log_levels:
            raise ValueError(f"無效的日誌等級: {settings.log_level}")
        
        return True
        
    except ValueError as e:
        print(f"配置驗證失敗: {e}")
        return False


# 在匯入時驗證配置
if not validate_settings(settings):
    print("警告: 配置驗證失敗，請檢查設定值")


def print_settings_summary():
    """列印配置摘要（用於除錯）"""
    print("=== OCR 服務配置摘要 ===")
    print(f"服務地址: {settings.host}:{settings.port}")
    print(f"除錯模式: {settings.debug}")
    print(f"最大檔案大小: {settings.max_file_size / 1024 / 1024:.1f} MB")
    print(f"OCR 語言: {settings.ocr_language}")
    print(f"使用 GPU: {settings.ocr_use_gpu}")
    print(f"啟用 MKL-DNN: {settings.ocr_enable_mkldnn}")
    print(f"CPU 執行緒數: {settings.ocr_cpu_threads}")
    print(f"最大併發請求: {settings.max_concurrent_requests}")
    print(f"日誌等級: {settings.log_level}")
    print("========================")


if __name__ == "__main__":
    # 用於測試配置載入
    print_settings_summary()