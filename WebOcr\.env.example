# OCR 服務環境變數配置範例
# 複製此檔案為 .env 並根據需要修改設定值

# 基本服務配置
OCR_APP_NAME="Advanced OCR Service"
OCR_DEBUG=false
OCR_HOST=0.0.0.0
OCR_PORT=8086

# 檔案上傳限制
OCR_MAX_FILE_SIZE=10485760  # 10MB in bytes

# PaddleOCR 配置
OCR_LANGUAGE=ch
OCR_USE_GPU=false
OCR_GPU_MEM=500

# CPU 優化設定
OCR_ENABLE_MKLDNN=true
OCR_CPU_THREADS=8

# 高性能設定
OCR_ENABLE_HPI=false
OCR_USE_TENSORRT=false
OCR_PRECISION=fp32

# 併發控制
OCR_MAX_CONCURRENT=5
OCR_REQUEST_TIMEOUT=30

# 日誌配置
OCR_LOG_LEVEL=INFO
OCR_LOG_FORMAT=json

# 監控配置
OCR_ENABLE_METRICS=true
OCR_METRICS_PORT=8002

# GPU 環境範例 (取消註解以啟用)
# OCR_USE_GPU=true
# OCR_USE_TENSORRT=true
# OCR_PRECISION=fp16
# OCR_ENABLE_HPI=true

# 高負載環境範例
# OCR_MAX_CONCURRENT=10
# OCR_CPU_THREADS=16