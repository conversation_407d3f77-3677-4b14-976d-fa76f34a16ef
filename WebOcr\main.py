import cv2
import numpy as np
import asyncio
import time
import json
from contextlib import asynccontextmanager
from typing import Dict, Any, Optional
from fastapi import FastAPI, File, UploadFile, HTTPException, status, Depends, Request
from fastapi.responses import JSONResponse
from fastapi.middleware.cors import CORSMiddleware
from paddleocr import PaddleOCR
import uvicorn
import structlog
from loguru import logger

from config import get_settings
from validators import validate_uploaded_file, get_file_info
from models import (
    OCRSuccessResponse,
    OCRSuccessData,
    OCRMetadata,
    ExtractedText,
    FileInfo,
    HealthResponse,
    ReadinessResponse,
    ValidationErrorResponse,
    ServerErrorResponse,
    ServiceUnavailableResponse,
)
from debug_helpers import debug_paddleocr_result, simple_debug_paddleocr

# --- 1. 全域變數和配置 ---
settings = get_settings()
ocr_engine: Optional[PaddleOCR] = None
request_semaphore: Optional[asyncio.Semaphore] = None

# 配置結構化日誌
structlog.configure(
    processors=[
        structlog.stdlib.filter_by_level,
        structlog.stdlib.add_logger_name,
        structlog.stdlib.add_log_level,
        structlog.stdlib.PositionalArgumentsFormatter(),
        structlog.processors.TimeStamper(fmt="iso"),
        structlog.processors.StackInfoRenderer(),
        structlog.processors.format_exc_info,
        structlog.processors.JSONRenderer()
        if settings.log_format == "json"
        else structlog.dev.ConsoleRenderer(),
    ],
    context_class=dict,
    logger_factory=structlog.stdlib.LoggerFactory(),
    cache_logger_on_first_use=True,
)

struct_logger = structlog.get_logger()


# --- 2. 應用程式生命週期管理 ---
@asynccontextmanager
async def lifespan(app: FastAPI):
    """應用程式生命週期管理"""
    # 啟動時初始化
    await startup_event()

    yield

    # 關閉時清理
    await shutdown_event()


# --- 3. 初始化 FastAPI 應用 ---
app = FastAPI(
    title=settings.app_name,
    description="高性能 OCR 服務，支援多種圖片格式的文字識別",
    version="2.0.0",
    lifespan=lifespan,
    debug=settings.debug,
)

# 添加 CORS 中介軟體
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


# --- 4. 應用程式事件處理 ---
async def startup_event():
    """應用程式啟動事件"""
    global ocr_engine, request_semaphore

    struct_logger.info(
        "正在啟動 OCR 服務...",
        settings={
            "app_name": settings.app_name,
            "version": "2.0.0",
            "debug": settings.debug,
        },
    )

    # 初始化併發控制
    request_semaphore = asyncio.Semaphore(settings.max_concurrent_requests)
    struct_logger.info(f"設定最大併發請求數: {settings.max_concurrent_requests}")

    # 初始化 PaddleOCR 引擎
    try:
        struct_logger.info(
            "正在初始化 PaddleOCR 引擎...",
            gpu_enabled=settings.ocr_use_gpu,
            mkldnn_enabled=settings.ocr_enable_mkldnn,
        )

        # 初始化 PaddleOCR，依設定啟用 GPU 與 MKL-DNN
        # 在 PaddleOCR v3.x 建議使用 device 參數切換 CPU/GPU；
        # MKL-DNN 僅於 CPU 推論時生效，故當 device=cpu 時 enable_mkldnn 會帶來效能提升。
        device = "gpu" if settings.ocr_use_gpu else "cpu"
        ocr_engine = PaddleOCR(
            lang=settings.ocr_language,
            device=device,
            enable_mkldnn=settings.ocr_enable_mkldnn,
        )

        struct_logger.info("PaddleOCR 引擎初始化成功")

    except Exception as e:
        struct_logger.error("初始化 PaddleOCR 引擎失敗", error=str(e))
        raise


async def shutdown_event():
    """應用程式關閉事件"""
    struct_logger.info("正在關閉 OCR 服務...")
    # 清理資源（如果需要）
    struct_logger.info("服務已關閉")


# --- 5. 工具函數 ---


def _is_ocr_line(item) -> bool:
    """檢查一個項目是否為有效的 OCR 行結果"""
    # line 形如 [box_points, (text, score)]
    return (
        isinstance(item, (list, tuple))
        and len(item) == 2
        and isinstance(item[0], (list, tuple))
        and isinstance(item[1], (list, tuple))
        and len(item[1]) >= 2
        and isinstance(item[1][0], (str, bytes))
    )


def process_ocr_result(
    result, request_id: str
) -> tuple[list[ExtractedText], float, int]:
    """
    處理 PaddleOCR 的返回結果，支援單筆和批次格式

    Args:
        result: PaddleOCR 的返回結果
        request_id: 請求 ID用於日誌記錄

    Returns:
        tuple: (extracted_texts, total_confidence, text_count)
    """
    extracted_texts = []
    total_confidence = 0.0
    text_count = 0

    # PaddleOCR 回傳格式可能有兩種：
    # 1) 單筆影像：result 為 [line, line, ...]
    # 2) 批次影像：result 為 [[line, line, ...]]
    lines = []
    if isinstance(result, list) and len(result) > 0:
        first = result[0]
        # 調試 PaddleOCR 回傳值 - 使用正確的 Python 方法
        try:
            with open(f"ocr_debug_{request_id}.txt", "w", encoding="utf-8") as f:
                f.write("=== PaddleOCR 回傳值調試 ===\n")
                f.write(f"完整 result 類型: {type(result)}\n")
                f.write(f"完整 result 內容: {str(result)}\n\n")

                f.write(f"first 類型: {type(first)}\n")
                f.write(f"first 內容: {str(first)}\n")
                f.write(f"first repr: {repr(first)}\n\n")

                # 如果是列表，分析每個元素
                if isinstance(first, list):
                    f.write(f"first 是列表，包含 {len(first)} 個元素:\n")
                    for i, item in enumerate(first[:5]):  # 只顯示前5個元素
                        f.write(f"  元素 {i}: {repr(item)}\n")
                    if len(first) > 5:
                        f.write(f"  ... 還有 {len(first) - 5} 個元素\n")

                struct_logger.info(f"調試檔案已建立: ocr_debug_{request_id}.txt")
        except Exception as debug_error:
            struct_logger.warning(f"建立調試檔案失敗: {debug_error}")
        if _is_ocr_line(first):
            # 單筆影像結果
            lines = result
        elif isinstance(first, list) and len(first) > 0 and _is_ocr_line(first[0]):
            # 批次影像結果，取第一筆
            lines = first

    if lines:
        for line in lines:
            try:
                text = line[1][0]
                confidence = float(line[1][1])
                # box 為四點座標
                position = [[float(coord[0]), float(coord[1])] for coord in line[0]]

                extracted_text = ExtractedText(
                    text=text, confidence=confidence, position=position
                )
                extracted_texts.append(extracted_text)
                total_confidence += confidence
                text_count += 1
            except Exception as parse_err:
                # 若單行解析失敗，記錄並略過該行
                struct_logger.warning(
                    "跳過無法解析的 OCR 行",
                    request_id=request_id,
                    line=line,
                    error=str(parse_err),
                )
    else:
        struct_logger.info(
            "OCR 沒有返回內容或格式不符",
            request_id=request_id,
            raw_type=str(type(result)),
        )

    return extracted_texts, total_confidence, text_count


def create_ocr_response(
    extracted_texts: list[ExtractedText],
    total_confidence: float,
    text_count: int,
    processing_duration: float,
    ocr_duration: float,
    file_info_dict: dict,
    request_id: str,
) -> OCRSuccessResponse:
    """
    建立 OCR 成功回應

    Args:
        extracted_texts: 識別出的文本列表
        total_confidence: 總信心分數
        text_count: 文本數量
        processing_duration: 總處理時間
        ocr_duration: OCR 處理時間
        file_info_dict: 檔案資訊字典
        request_id: 請求 ID

    Returns:
        OCRSuccessResponse: 成功回應物件
    """
    avg_confidence = total_confidence / text_count if text_count > 0 else 0.0

    # 建立 FileInfo 實例
    file_info_obj = FileInfo(
        filename=file_info_dict.get("filename", "unknown.png"),
        content_type=file_info_dict.get("content_type", "image/png"),
        size=file_info_dict.get("size", 0),
        sanitized_filename=file_info_dict.get("sanitized_filename", "unknown.png"),
    )

    # 建立 OCRMetadata 實例
    metadata = OCRMetadata(
        total_texts=text_count,
        average_confidence=round(avg_confidence, 3),
        processing_time=round(processing_duration, 3),
        ocr_time=round(ocr_duration, 3),
        file_info=file_info_obj,
    )

    # 建立 OCRSuccessData 實例
    success_data = OCRSuccessData(texts=extracted_texts, metadata=metadata)

    # 建立並回傳 OCRSuccessResponse 實例
    return OCRSuccessResponse(
        status="success", data=success_data, request_id=request_id
    )


async def execute_ocr_process(
    preprocessed_image, request_id: str
) -> tuple[list[ExtractedText], float, int, float]:
    """
    執行 OCR 處理流程

    Args:
        preprocessed_image: 預處理後的影像
        request_id: 請求 ID

    Returns:
        tuple: (extracted_texts, total_confidence, text_count, ocr_duration)

    Raises:
        HTTPException: 當 OCR 引擎未初始化時
    """
    if ocr_engine is None:
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail={"error": "OCR_ENGINE_NOT_READY", "message": "OCR 引擎未初始化"},
        )

    ocr_start = time.time()
    result = ocr_engine.predict(preprocessed_image)
    ocr_duration = time.time() - ocr_start

    extracted_texts, total_confidence, text_count = process_ocr_result(
        result, request_id
    )

    return extracted_texts, total_confidence, text_count, ocr_duration


def preprocess_for_ocr(image_bytes: bytes) -> np.ndarray:
    """
    對輸入的圖片進行完整的預處理流程，專門針對 PaddleOCR 優化。

    處理策略：
    - 歪斜角度計算：使用灰階圖像（更準確）
    - 最終輸出：彩色 RGB 圖像（PaddleOCR 最佳效果）

    Args:
        image_bytes: 原始圖片的字節數據

    Returns:
        np.ndarray: 預處理後的 RGB 圖像，適合 PaddleOCR 處理
    """
    # 1. 將字節流轉換為 OpenCV 圖像格式
    nparr = np.frombuffer(image_bytes, np.uint8)
    img = cv2.imdecode(nparr, cv2.IMREAD_COLOR)  # BGR 格式

    if img is None:
        raise ValueError("無法解碼圖片，請確認圖片格式正確")

    # 2. 歪斜角度計算（使用灰階圖像，計算更準確）
    gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
    _, binary_for_skew = cv2.threshold(
        gray, 0, 255, cv2.THRESH_BINARY_INV + cv2.THRESH_OTSU
    )

    # 計算傾斜角度
    coords = np.column_stack(np.where(binary_for_skew > 0))
    if len(coords) > 0:  # 確保有足夠的像素點
        angle = cv2.minAreaRect(coords)[-1]
        # 角度修正
        if angle < -45:
            angle = -(90 + angle)
        else:
            angle = -angle
    else:
        angle = 0  # 如果無法計算角度，不進行旋轉

    # 3. 歪斜校正（對彩色圖像進行旋轉）
    (h, w) = img.shape[:2]
    center = (w // 2, h // 2)
    M = cv2.getRotationMatrix2D(center, angle, 1.0)
    rotated = cv2.warpAffine(
        img,
        M,
        (w, h),
        flags=cv2.INTER_CUBIC,
        borderMode=cv2.BORDER_CONSTANT,
        borderValue=(255, 255, 255),  # 白色背景
    )

    # 4. 雜訊移除（對彩色圖像進行）
    denoised = cv2.medianBlur(rotated, settings.preprocessing_median_blur_kernel)

    # 5. 對比度和亮度增強（可選，保持彩色）
    # 使用 CLAHE 對每個通道進行直方圖均衡化
    lab = cv2.cvtColor(denoised, cv2.COLOR_BGR2LAB)
    clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8, 8))
    lab[:, :, 0] = clahe.apply(lab[:, :, 0])
    enhanced = cv2.cvtColor(lab, cv2.COLOR_LAB2BGR)

    # 6. 轉換為 RGB 格式（PaddleOCR 接受 RGB）
    final_img = cv2.cvtColor(enhanced, cv2.COLOR_BGR2RGB)

    return final_img


# --- 6. API 端點 ---


@app.get(
    "/health",
    response_model=HealthResponse,
    summary="健康檢查",
    description="檢查服務是否正常運行",
    tags=["Health"],
)
async def health_check() -> HealthResponse:
    """
    健康檢查端點

    這個端點用於檢查服務是否正常運行，通常用於負載均衡器和監控系統。

    Returns:
        HealthResponse: 健康狀態資訊
    """
    return HealthResponse(
        status="healthy",
        service=settings.app_name,
        version="2.0.0",
        timestamp=time.time(),
    )


@app.get(
    "/ready",
    response_model=ReadinessResponse,
    summary="就緒檢查",
    description="檢查服務是否就緒並可以接受請求",
    tags=["Health"],
    responses={503: {"model": ServiceUnavailableResponse, "description": "服務未就緒"}},
)
async def readiness_check() -> ReadinessResponse:
    """
    服務就緒檢查端點

    檢查服務是否已經初始化完成並可以接受請求。與健康檢查不同，
    這個檢查會驗證關鍵組件（如 OCR 引擎）是否已初始化。

    Returns:
        ReadinessResponse: 服務就緒狀態

    Raises:
        HTTPException: 當 OCR 引擎未初始化時拋出 503 错誤
    """
    if ocr_engine is None:
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail={"error": "SERVICE_NOT_READY", "message": "OCR 引擎未初始化"},
        )

    return ReadinessResponse(
        status="ready",
        service=settings.app_name,
        ocr_engine_ready=ocr_engine is not None,
        max_concurrent_requests=settings.max_concurrent_requests,
    )


@app.post(
    "/ocr",
    response_model=OCRSuccessResponse,
    summary="圖片文字識別",
    description="上傳圖片並執行 OCR 文字識別，支援多種圖片格式",
    tags=["OCR"],
    responses={
        400: {
            "model": ValidationErrorResponse,
            "description": "檔案驗證失敗或參數錯誤",
        },
        500: {"model": ServerErrorResponse, "description": "伺服器內部錯誤"},
        503: {
            "model": ServiceUnavailableResponse,
            "description": "服務不可用或 OCR 引擎未就緒",
        },
    },
)
async def perform_ocr(
    file: UploadFile = File(
        ..., description="要進行 OCR 識別的圖片檔案，支援 JPG、PNG、BMP、TIFF 等格式"
    ),
) -> OCRSuccessResponse:
    """
    執行圖片文字識別 (OCR) 作業

    這個 API 端點接收上傳的圖片檔案，進行預處理後使用 PaddleOCR
    執行文字識別。支援識別中文、英文等多種語言。

    功能特色：
    - 自動檔案驗證（大小、格式、安全性）
    - 智慧圖片預處理（歪斜校正、雜訊移除、二值化）
    - 高性能識別引擎（支援 GPU 加速、MKL-DNN 優化）
    - 併發控制和資源管理
    - 結構化日誌記錄

    支援的檔案格式：
    - JPG/JPEG
    - PNG
    - BMP
    - TIFF
    - WebP

    Args:
        file: 上傳的圖片檔案

    Returns:
        OCRSuccessResponse: 包含識別結果和元數據的響應

    Raises:
        HTTPException:
            - 400: 檔案驗證失敗（格式不支援、大小超過限制等）
            - 500: 識別過程發生錯誤
            - 503: 服務不可用或達到併發限制

    Example:
        ```python
        import requests

        with open('document.jpg', 'rb') as f:
            files = {'file': f}
            response = requests.post('http://localhost:8086/ocr', files=files)
            result = response.json()
        ```
    """
    # 併發控制
    if request_semaphore is None:
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail={"error": "SERVICE_NOT_READY", "message": "服務未就緒"},
        )

    async with request_semaphore:
        start_time = time.time()
        request_id = f"req_{int(start_time * 1000)}"

        struct_logger.info(
            "開始處理 OCR 請求", request_id=request_id, filename=file.filename
        )

        try:
            # --- 檔案驗證 ---
            await validate_uploaded_file(file)
            file_info = get_file_info(file)

            struct_logger.info(
                "檔案驗證通過", request_id=request_id, file_info=file_info
            )

            # --- 讀取檔案內容 ---
            image_bytes = await file.read()

            # --- 預處理圖片 ---
            preprocessed_image = preprocess_for_ocr(image_bytes)

            struct_logger.info(
                "圖片預處理完成",
                request_id=request_id,
                image_shape=preprocessed_image.shape,
            )

            # --- 執行 OCR ---
            (
                extracted_texts,
                total_confidence,
                text_count,
                ocr_duration,
            ) = await execute_ocr_process(preprocessed_image, request_id)

            processing_duration = time.time() - start_time
            avg_confidence = total_confidence / text_count if text_count > 0 else 0.0

            struct_logger.info(
                "識別完成",
                request_id=request_id,
                text_count=text_count,
                avg_confidence=avg_confidence,
                ocr_duration=ocr_duration,
                total_duration=processing_duration,
            )

            # 使用提取的函數建立回應
            return create_ocr_response(
                extracted_texts=extracted_texts,
                total_confidence=total_confidence,
                text_count=text_count,
                processing_duration=processing_duration,
                ocr_duration=ocr_duration,
                file_info_dict=file_info,
                request_id=request_id,
            )

        except HTTPException:
            # 重新拋出 HTTP 異常
            raise

        except Exception as e:
            error_msg = str(e)
            struct_logger.error(
                "處理 OCR 請求時發生錯誤",
                request_id=request_id,
                error=error_msg,
                filename=file.filename,
            )

            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail={
                    "error": "OCR_PROCESSING_ERROR",
                    "message": "處理圖片時發生錯誤",
                    "request_id": request_id,
                },
            )


@app.post(
    "/ocr-bytes",
    response_model=OCRSuccessResponse,
    summary="Bytes 圖片文字識別",
    description="直接接收 bytes 圖片資料並執行 OCR 文字識別",
    tags=["OCR"],
    responses={
        400: {
            "model": ValidationErrorResponse,
            "description": "檔案驗證失敗或參數錯誤",
        },
        500: {"model": ServerErrorResponse, "description": "伺服器內部錯誤"},
        503: {
            "model": ServiceUnavailableResponse,
            "description": "服務不可用或 OCR 引擎未就緒",
        },
    },
)
async def perform_ocr_bytes(
    request: Request, filename: str = "image.png"
) -> OCRSuccessResponse:
    """
    執行 Bytes 圖片文字識別 (OCR) 作業

    這個 API 端點接收 bytes 圖片資料，進行預處理後使用 PaddleOCR
    執行文字識別。支援識別中文、英文等多種語言。

    Args:
        request: HTTP 請求物件，包含 bytes 圖片資料
        filename: 檔案名稱（預設為 image.png）

    Returns:
        OCRSuccessResponse: 包含識別結果和元数據的響應

    Raises:
        HTTPException:
            - 400: 資料驗證失敗
            - 500: 識別過程發生錯誤
            - 503: 服務不可用或達到併發限制
    """
    # 併發控制
    if request_semaphore is None:
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail={"error": "SERVICE_NOT_READY", "message": "服務未就緒"},
        )

    async with request_semaphore:
        start_time = time.time()
        request_id = f"req_bytes_{int(start_time * 1000)}"

        struct_logger.info(
            "開始處理 OCR bytes 請求", request_id=request_id, filename=filename
        )

        try:
            # --- 讀取原始 bytes 資料 ---
            image_bytes = await request.body()

            if not image_bytes:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail={"error": "EMPTY_IMAGE_DATA", "message": "圖片資料不能為空"},
                )

            struct_logger.info(
                "成功接收 bytes 資料", request_id=request_id, data_size=len(image_bytes)
            )

            # --- 預處理圖片 ---
            preprocessed_image = preprocess_for_ocr(image_bytes)

            struct_logger.info(
                "圖片預處理完成",
                request_id=request_id,
                image_shape=preprocessed_image.shape,
            )

            # --- 執行 OCR ---
            (
                extracted_texts,
                total_confidence,
                text_count,
                ocr_duration,
            ) = await execute_ocr_process(preprocessed_image, request_id)

            processing_duration = time.time() - start_time
            avg_confidence = total_confidence / text_count if text_count > 0 else 0.0

            struct_logger.info(
                "識別完成 (bytes)",
                request_id=request_id,
                text_count=text_count,
                avg_confidence=avg_confidence,
                ocr_duration=ocr_duration,
                total_duration=processing_duration,
            )

            # 準備 bytes 檔案資訊
            file_info_dict = {
                "filename": filename,
                "content_type": "image/png",
                "size": len(image_bytes),
                "sanitized_filename": filename,
            }

            # 使用提取的函數建立回應
            return create_ocr_response(
                extracted_texts=extracted_texts,
                total_confidence=total_confidence,
                text_count=text_count,
                processing_duration=processing_duration,
                ocr_duration=ocr_duration,
                file_info_dict=file_info_dict,
                request_id=request_id,
            )

        except HTTPException:
            # 重新拋出 HTTP 異常
            raise

        except Exception as e:
            error_msg = str(e)
            struct_logger.error(
                "處理 OCR bytes 請求時發生錯誤",
                request_id=request_id,
                error=error_msg,
                filename=filename,
            )

            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail={
                    "error": "OCR_PROCESSING_ERROR",
                    "message": "處理 bytes 圖片時發生錯誤",
                    "request_id": request_id,
                },
            )


# --- 7. 服務啟動 ---
if __name__ == "__main__":
    # 使用配置中的參數啟動服務
    uvicorn.run(
        app,
        host=settings.host,
        port=settings.port,
        log_level=settings.log_level.lower(),
        access_log=True,
    )
