"""
檔案上傳驗證模組
提供全面的檔案上傳安全性檢查功能
"""

import magic
import mimetypes
from pathlib import Path
from typing import Set, Optional, Tuple
from fastapi import UploadFile, HTTPException, status
import numpy as np
import cv2

from config import get_settings


class FileValidator:
    """檔案驗證器"""
    
    def __init__(self):
        self.settings = get_settings()
        # 初始化 python-magic (用於更準確的 MIME 類型檢測)
        try:
            self.magic_mime = magic.Magic(mime=True)
        except Exception:
            self.magic_mime = None
            print("警告: python-magic 不可用，將使用替代方案進行 MIME 類型檢測")
    
    async def validate_file(self, file: UploadFile) -> Tuple[bool, str]:
        """
        完整的檔案驗證
        
        Args:
            file: 上傳的檔案
            
        Returns:
            Tuple[bool, str]: (是否通過驗證, 錯誤訊息)
        """
        try:
            # 1. 檢查檔案是否存在
            if not file or not file.filename:
                return False, "未提供有效的檔案"
            
            # 2. 檢查檔案副檔名
            is_valid_ext, ext_message = self._validate_file_extension(file.filename)
            if not is_valid_ext:
                return False, ext_message
            
            # 3. 讀取檔案內容進行進一步檢查
            file_content = await file.read()
            
            # 4. 檢查檔案大小
            is_valid_size, size_message = self._validate_file_size(len(file_content))
            if not is_valid_size:
                return False, size_message
            
            # 5. 檢查 MIME 類型
            is_valid_mime, mime_message = self._validate_mime_type(file_content, file.filename)
            if not is_valid_mime:
                return False, mime_message
            
            # 6. 驗證圖片格式和內容
            is_valid_image, image_message = self._validate_image_content(file_content)
            if not is_valid_image:
                return False, image_message
            
            # 7. 重置檔案指標供後續使用
            await file.seek(0)
            
            return True, "檔案驗證通過"
            
        except Exception as e:
            return False, f"檔案驗證過程發生錯誤: {str(e)}"
    
    def _validate_file_extension(self, filename: str) -> Tuple[bool, str]:
        """驗證檔案副檔名"""
        file_path = Path(filename)
        file_ext = file_path.suffix.lower()
        
        if not file_ext:
            return False, "檔案必須具有副檔名"
        
        if file_ext not in self.settings.allowed_extensions:
            allowed_exts = ", ".join(self.settings.allowed_extensions)
            return False, f"不支援的檔案格式 {file_ext}。支援的格式: {allowed_exts}"
        
        return True, ""
    
    def _validate_file_size(self, file_size: int) -> Tuple[bool, str]:
        """驗證檔案大小"""
        if file_size == 0:
            return False, "檔案不能為空"
        
        if file_size > self.settings.max_file_size:
            max_size_mb = self.settings.max_file_size / (1024 * 1024)
            current_size_mb = file_size / (1024 * 1024)
            return False, f"檔案過大 ({current_size_mb:.1f}MB)，最大允許 {max_size_mb:.1f}MB"
        
        return True, ""
    
    def _validate_mime_type(self, file_content: bytes, filename: str) -> Tuple[bool, str]:
        """驗證 MIME 類型"""
        detected_mime = None
        
        # 嘗試使用 python-magic 檢測 MIME 類型
        if self.magic_mime:
            try:
                detected_mime = self.magic_mime.from_buffer(file_content)
            except Exception:
                pass
        
        # 如果 magic 失敗，使用 mimetypes 作為後備
        if not detected_mime:
            detected_mime, _ = mimetypes.guess_type(filename)
        
        if not detected_mime:
            return False, "無法檢測檔案類型"
        
        if detected_mime not in self.settings.allowed_mime_types:
            allowed_mimes = ", ".join(self.settings.allowed_mime_types)
            return False, f"不支援的檔案類型 {detected_mime}。支援的類型: {allowed_mimes}"
        
        return True, ""
    
    def _validate_image_content(self, file_content: bytes) -> Tuple[bool, str]:
        """驗證圖片內容是否有效"""
        try:
            # 使用 OpenCV 嘗試解碼圖片
            nparr = np.frombuffer(file_content, np.uint8)
            img = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
            
            if img is None:
                return False, "無效的圖片檔案：無法解析圖片內容"
            
            # 檢查圖片尺寸
            height, width = img.shape[:2]
            
            if height < 10 or width < 10:
                return False, "圖片尺寸過小，最小尺寸為 10x10 像素"
            
            if height > 10000 or width > 10000:
                return False, "圖片尺寸過大，最大尺寸為 10000x10000 像素"
            
            # 檢查圖片通道數
            if len(img.shape) == 3 and img.shape[2] not in [1, 3, 4]:
                return False, "不支援的圖片通道數"
            
            return True, ""
            
        except Exception as e:
            return False, f"圖片內容驗證失敗: {str(e)}"


class SecurityValidator:
    """安全性驗證器"""
    
    @staticmethod
    def validate_filename(filename: str) -> Tuple[bool, str]:
        """驗證檔案名稱安全性"""
        if not filename:
            return False, "檔案名稱不能為空"
        
        # 檢查危險字元
        dangerous_chars = ['<', '>', ':', '"', '|', '?', '*', '\0']
        for char in dangerous_chars:
            if char in filename:
                return False, f"檔案名稱包含不安全字元: {char}"
        
        # 檢查路徑穿越嘗試
        if '..' in filename or filename.startswith('/') or '\\' in filename:
            return False, "檔案名稱包含不安全路徑"
        
        # 檢查檔案名稱長度
        if len(filename) > 255:
            return False, "檔案名稱過長"
        
        return True, ""
    
    @staticmethod
    def sanitize_filename(filename: str) -> str:
        """清理檔案名稱"""
        # 移除危險字元
        dangerous_chars = ['<', '>', ':', '"', '|', '?', '*', '\0', '..', '/', '\\']
        sanitized = filename
        for char in dangerous_chars:
            sanitized = sanitized.replace(char, '_')
        
        # 限制長度
        if len(sanitized) > 255:
            name_part = Path(sanitized).stem[:200]
            ext_part = Path(sanitized).suffix
            sanitized = name_part + ext_part
        
        return sanitized


# 全域驗證器實例
file_validator = FileValidator()
security_validator = SecurityValidator()


async def validate_uploaded_file(file: UploadFile) -> None:
    """
    驗證上傳檔案的便利函式
    
    Args:
        file: 上傳的檔案
        
    Raises:
        HTTPException: 當驗證失敗時拋出
    """
    # 安全性驗證
    is_safe, safe_message = security_validator.validate_filename(file.filename or "")
    if not is_safe:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail={
                "error": "FILENAME_SECURITY_ERROR",
                "message": safe_message
            }
        )
    
    # 檔案內容驗證
    is_valid, validation_message = await file_validator.validate_file(file)
    if not is_valid:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail={
                "error": "FILE_VALIDATION_ERROR",
                "message": validation_message
            }
        )


def get_file_info(file: UploadFile) -> dict:
    """
    獲取檔案基本資訊
    
    Args:
        file: 上傳的檔案
        
    Returns:
        dict: 檔案資訊
    """
    return {
        "filename": file.filename,
        "content_type": file.content_type,
        "size": file.size if hasattr(file, 'size') else None,
        "sanitized_filename": security_validator.sanitize_filename(file.filename or "unknown")
    }