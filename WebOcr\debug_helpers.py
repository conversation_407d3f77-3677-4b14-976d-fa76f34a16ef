"""
PaddleOCR 調試輔助工具
提供多種方式來查看和分析 PaddleOCR 的回傳值
"""

import json
import pprint
from typing import Any


def debug_paddleocr_result(result: Any, request_id: str, save_to_file: bool = True) -> str:
    """
    全面分析 PaddleOCR 回傳值的調試函數
    
    Args:
        result: PaddleOCR 的回傳結果
        request_id: 請求 ID
        save_to_file: 是否儲存到檔案
    
    Returns:
        str: 調試資訊字串
    """
    debug_info = []
    
    # 基本資訊
    debug_info.append("=== PaddleOCR 回傳值分析 ===")
    debug_info.append(f"類型: {type(result)}")
    debug_info.append(f"是否為空: {result is None}")
    
    if hasattr(result, '__len__'):
        debug_info.append(f"長度: {len(result)}")
    
    # 詳細內容
    debug_info.append("\n=== 使用 str() 轉換 ===")
    debug_info.append(str(result))
    
    debug_info.append("\n=== 使用 repr() 轉換 ===")
    debug_info.append(repr(result))
    
    # 如果是列表，分析每個元素
    if isinstance(result, list):
        debug_info.append(f"\n=== 列表分析 (共 {len(result)} 個元素) ===")
        for i, item in enumerate(result):
            debug_info.append(f"\n元素 {i}:")
            debug_info.append(f"  類型: {type(item)}")
            debug_info.append(f"  內容: {repr(item)}")
            
            # 如果是 OCR 結果格式
            if isinstance(item, (list, tuple)) and len(item) >= 2:
                debug_info.append(f"  可能的 OCR 格式:")
                debug_info.append(f"    座標: {item[0]}")
                debug_info.append(f"    文字資訊: {item[1]}")
                
                if isinstance(item[1], (list, tuple)) and len(item[1]) >= 2:
                    debug_info.append(f"      文字: {item[1][0]}")
                    debug_info.append(f"      信心度: {item[1][1]}")
    
    # 嘗試 JSON 序列化
    debug_info.append("\n=== JSON 序列化嘗試 ===")
    try:
        json_str = json.dumps(result, ensure_ascii=False, indent=2, default=str)
        debug_info.append("成功:")
        debug_info.append(json_str)
    except Exception as e:
        debug_info.append(f"失敗: {e}")
    
    # 使用 pprint 美化輸出
    debug_info.append("\n=== pprint 美化輸出 ===")
    try:
        pp = pprint.PrettyPrinter(indent=2, width=80)
        debug_info.append(pp.pformat(result))
    except Exception as e:
        debug_info.append(f"pprint 失敗: {e}")
    
    debug_text = "\n".join(debug_info)
    
    # 儲存到檔案
    if save_to_file:
        filename = f"paddleocr_debug_{request_id}.txt"
        with open(filename, "w", encoding="utf-8") as f:
            f.write(debug_text)
        print(f"調試資訊已儲存到: {filename}")
    
    return debug_text


def simple_debug_paddleocr(result: Any, request_id: str = "unknown") -> None:
    """
    簡單的 PaddleOCR 調試函數，直接印出基本資訊
    
    Args:
        result: PaddleOCR 的回傳結果
        request_id: 請求 ID
    """
    print(f"\n=== PaddleOCR 調試 (Request: {request_id}) ===")
    print(f"類型: {type(result)}")
    
    if hasattr(result, '__len__'):
        print(f"長度: {len(result)}")
    
    print(f"內容: {str(result)}")
    
    if isinstance(result, list) and len(result) > 0:
        print(f"第一個元素類型: {type(result[0])}")
        print(f"第一個元素: {repr(result[0])}")


def analyze_ocr_structure(result: Any) -> dict:
    """
    分析 OCR 結果的結構
    
    Args:
        result: PaddleOCR 的回傳結果
    
    Returns:
        dict: 結構分析結果
    """
    analysis = {
        "type": str(type(result)),
        "is_none": result is None,
        "has_length": hasattr(result, '__len__'),
        "length": len(result) if hasattr(result, '__len__') else None,
        "is_list": isinstance(result, list),
        "is_empty": len(result) == 0 if hasattr(result, '__len__') else None,
    }
    
    if isinstance(result, list) and len(result) > 0:
        first_item = result[0]
        analysis["first_item"] = {
            "type": str(type(first_item)),
            "is_list": isinstance(first_item, list),
            "length": len(first_item) if hasattr(first_item, '__len__') else None,
        }
        
        # 檢查是否符合 OCR 行格式
        if isinstance(first_item, (list, tuple)) and len(first_item) >= 2:
            analysis["first_item"]["possible_ocr_line"] = True
            analysis["first_item"]["coordinates"] = first_item[0] if len(first_item) > 0 else None
            analysis["first_item"]["text_info"] = first_item[1] if len(first_item) > 1 else None
        else:
            analysis["first_item"]["possible_ocr_line"] = False
    
    return analysis


# 使用範例
if __name__ == "__main__":
    # 模擬 PaddleOCR 結果
    mock_result = [
        [[[100, 50], [200, 50], [200, 80], [100, 80]], ("Hello World", 0.95)],
        [[[100, 90], [250, 90], [250, 120], [100, 120]], ("測試文字", 0.88)]
    ]
    
    print("=== 測試調試工具 ===")
    simple_debug_paddleocr(mock_result, "test")
    
    analysis = analyze_ocr_structure(mock_result)
    print(f"\n結構分析: {analysis}")
