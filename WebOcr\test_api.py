"""
OCR API 功能測試腳本
用於驗證改善後的 API 是否正常運作
"""

import requests
import time
import io
from PIL import Image, ImageDraw, ImageFont


class OCRAPITester:
    def __init__(self, base_url: str = "http://localhost:8086"):
        self.base_url = base_url
        self.session = requests.Session()
        self.test_results = []
    
    def create_test_image(self) -> bytes:
        """創建一個簡單的測試圖片"""
        # 創建一個白底黑字的測試圖片
        img = Image.new('RGB', (400, 100), color='white')
        draw = ImageDraw.Draw(img)
        
        # 添加測試文字
        text = "Hello World 你好世界 123"
        try:
            # 嘗試使用系統字體
            font = ImageFont.truetype("arial.ttf", 24)
        except:
            # 如果找不到字體，使用默認字體
            font = ImageFont.load_default()
        
        draw.text((10, 30), text, fill='black', font=font)
        
        # 轉換為 bytes
        img_byte_arr = io.BytesIO()
        img.save(img_byte_arr, format='PNG')
        img_byte_arr = img_byte_arr.getvalue()
        
        return img_byte_arr
    
    def test_health_check(self):
        """測試健康檢查端點"""
        print("🔍 測試健康檢查端點...")
        try:
            response = self.session.get(f"{self.base_url}/health")
            if response.status_code == 200:
                data = response.json()
                print(f"✅ 健康檢查通過: {data.get('status')}")
                return True
            else:
                print(f"❌ 健康檢查失敗: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ 健康檢查異常: {e}")
            return False
    
    def test_readiness_check(self):
        """測試就緒檢查端點"""
        print("🔍 測試就緒檢查端點...")
        try:
            response = self.session.get(f"{self.base_url}/ready")
            if response.status_code == 200:
                data = response.json()
                print(f"✅ 就緒檢查通過: OCR引擎就緒 = {data.get('ocr_engine_ready')}")
                return True
            else:
                print(f"❌ 就緒檢查失敗: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ 就緒檢查異常: {e}")
            return False
    
    def test_ocr_with_valid_image(self):
        """測試有效圖片的 OCR 識別"""
        print("🔍 測試 OCR 圖片識別...")
        try:
            # 創建測試圖片
            test_image = self.create_test_image()
            
            files = {
                'file': ('test_image.png', test_image, 'image/png')
            }
            
            start_time = time.time()
            response = self.session.post(f"{self.base_url}/ocr", files=files)
            processing_time = time.time() - start_time
            
            if response.status_code == 200:
                data = response.json()
                texts = data.get('data', {}).get('texts', [])
                metadata = data.get('data', {}).get('metadata', {})
                
                print(f"✅ OCR 識別成功:")
                print(f"   處理時間: {processing_time:.2f}s")
                print(f"   識別文字數量: {len(texts)}")
                print(f"   平均信心度: {metadata.get('average_confidence', 0):.3f}")
                
                for i, text_info in enumerate(texts[:3]):  # 顯示前3個結果
                    print(f"   文字 {i+1}: '{text_info.get('text')}' (信心度: {text_info.get('confidence', 0):.3f})")
                
                return True
            else:
                print(f"❌ OCR 識別失敗: {response.status_code}")
                print(f"   錯誤: {response.text}")
                return False
                
        except Exception as e:
            print(f"❌ OCR 識別異常: {e}")
            return False
    
    def test_file_validation(self):
        """測試檔案驗證功能"""
        print("🔍 測試檔案驗證...")
        
        # 測試無效檔案類型
        try:
            invalid_file = b"This is not an image"
            files = {
                'file': ('test.txt', invalid_file, 'text/plain')
            }
            
            response = self.session.post(f"{self.base_url}/ocr", files=files)
            
            if response.status_code == 400:
                print("✅ 檔案類型驗證正常工作")
                return True
            else:
                print(f"❌ 檔案驗證未正常工作: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ 檔案驗證測試異常: {e}")
            return False
    
    def test_concurrent_requests(self):
        """測試併發請求處理"""
        print("🔍 測試併發請求處理...")
        try:
            import threading
            import concurrent.futures
            
            test_image = self.create_test_image()
            
            def make_ocr_request():
                files = {
                    'file': ('test_image.png', test_image, 'image/png')
                }
                response = requests.post(f"{self.base_url}/ocr", files=files)
                return response.status_code == 200
            
            # 發送5個併發請求
            with concurrent.futures.ThreadPoolExecutor(max_workers=5) as executor:
                futures = [executor.submit(make_ocr_request) for _ in range(5)]
                results = [future.result() for future in concurrent.futures.as_completed(futures)]
            
            success_count = sum(results)
            print(f"✅ 併發測試完成: {success_count}/5 請求成功")
            
            return success_count >= 3  # 至少3個請求成功
            
        except Exception as e:
            print(f"❌ 併發測試異常: {e}")
            return False
    
    def run_all_tests(self):
        """執行所有測試"""
        print("🚀 開始執行 OCR API 測試套件")
        print("=" * 50)
        
        tests = [
            ("健康檢查", self.test_health_check),
            ("就緒檢查", self.test_readiness_check),
            ("OCR 識別", self.test_ocr_with_valid_image),
            ("檔案驗證", self.test_file_validation),
            ("併發處理", self.test_concurrent_requests)
        ]
        
        results = []
        for test_name, test_func in tests:
            print()
            success = test_func()
            results.append((test_name, success))
        
        print("\n" + "=" * 50)
        print("📊 測試結果總結:")
        
        total_tests = len(results)
        passed_tests = sum(1 for _, success in results if success)
        
        for test_name, success in results:
            status = "✅ 通過" if success else "❌ 失敗"
            print(f"  {test_name}: {status}")
        
        print(f"\n總計: {passed_tests}/{total_tests} 測試通過")
        
        if passed_tests == total_tests:
            print("🎉 所有測試都通過了！API 運作正常。")
        else:
            print("⚠️  部分測試失敗，請檢查 API 配置和服務狀態。")
        
        return passed_tests == total_tests


def main():
    """主函數"""
    print("OCR API 測試工具")
    print("=" * 50)
    
    # 檢查 API 服務是否可用
    tester = OCRAPITester()
    
    try:
        # 先檢查服務是否可達
        response = requests.get(f"{tester.base_url}/health", timeout=5)
        print(f"✅ API 服務已啟動 (狀態碼: {response.status_code})")
    except requests.exceptions.RequestException:
        print("❌ 無法連接到 API 服務")
        print("請確保 OCR 服務在 http://localhost:8086 上運行")
        print("啟動命令: python main.py")
        return
    
    # 執行所有測試
    success = tester.run_all_tests()
    
    if not success:
        exit(1)


if __name__ == "__main__":
    main()