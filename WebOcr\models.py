"""
API 響應模型定義
用於 FastAPI 的 OpenAPI 文檔生成和數據驗證
"""

from typing import List, Optional, Any
from pydantic import BaseModel, Field


class TextPosition(BaseModel):
    """文字位置坐標"""
    coordinates: List[List[float]] = Field(
        description="文字區域的四個角點坐標 [[x1,y1], [x2,y2], [x3,y3], [x4,y4]]",
        example=[[100.0, 50.0], [200.0, 50.0], [200.0, 80.0], [100.0, 80.0]]
    )


class ExtractedText(BaseModel):
    """識別出的文字內容"""
    text: str = Field(description="識別出的文字內容", example="這是識別出的文字")
    confidence: float = Field(
        description="識別信心度（0-1之間）",
        ge=0.0,
        le=1.0,
        example=0.95
    )
    position: List[List[float]] = Field(
        description="文字區域的四個角點坐標",
        example=[[100.0, 50.0], [200.0, 50.0], [200.0, 80.0], [100.0, 80.0]]
    )


class FileInfo(BaseModel):
    """檔案資訊"""
    filename: Optional[str] = Field(description="原始檔案名稱", example="document.jpg")
    content_type: Optional[str] = Field(description="檔案 MIME 類型", example="image/jpeg")
    size: Optional[int] = Field(description="檔案大小（位元組）", example=1024000)
    sanitized_filename: str = Field(description="清理後的安全檔案名稱", example="document.jpg")


class OCRMetadata(BaseModel):
    """OCR 處理元數據"""
    total_texts: int = Field(description="識別出的文字區塊總數", example=5)
    average_confidence: float = Field(
        description="平均信心度",
        ge=0.0,
        le=1.0,
        example=0.87
    )
    processing_time: float = Field(description="總處理時間（秒）", example=2.35)
    ocr_time: float = Field(description="OCR 識別時間（秒）", example=1.82)
    file_info: FileInfo = Field(description="檔案資訊")


class OCRSuccessData(BaseModel):
    """OCR 成功響應數據"""
    texts: List[ExtractedText] = Field(description="識別出的文字列表")
    metadata: OCRMetadata = Field(description="處理元數據")


class OCRSuccessResponse(BaseModel):
    """OCR 成功響應"""
    status: str = Field(default="success", description="響應狀態")
    data: OCRSuccessData = Field(description="OCR 識別結果")
    request_id: str = Field(description="請求唯一識別碼", example="req_1234567890")


class ErrorDetail(BaseModel):
    """錯誤詳情"""
    error: str = Field(description="錯誤代碼", example="FILE_VALIDATION_ERROR")
    message: str = Field(description="錯誤描述", example="檔案格式不支援")
    request_id: Optional[str] = Field(description="請求識別碼", example="req_1234567890")


class HealthResponse(BaseModel):
    """健康檢查響應"""
    status: str = Field(default="healthy", description="健康狀態")
    service: str = Field(description="服務名稱", example="Advanced OCR Service")
    version: str = Field(description="服務版本", example="2.0.0")
    timestamp: float = Field(description="檢查時間戳", example=**********.0)


class ReadinessResponse(BaseModel):
    """就緒檢查響應"""
    status: str = Field(default="ready", description="就緒狀態")
    service: str = Field(description="服務名稱", example="Advanced OCR Service")
    ocr_engine_ready: bool = Field(description="OCR 引擎是否就緒", example=True)
    max_concurrent_requests: int = Field(description="最大併發請求數", example=5)


# HTTP 錯誤響應模型
class ValidationErrorResponse(BaseModel):
    """驗證錯誤響應"""
    detail: ErrorDetail = Field(description="錯誤詳情")


class ServerErrorResponse(BaseModel):
    """伺服器錯誤響應"""
    detail: ErrorDetail = Field(description="錯誤詳情")


class ServiceUnavailableResponse(BaseModel):
    """服務不可用響應"""
    detail: ErrorDetail = Field(description="錯誤詳情")